#!/usr/bin/env node

/**
 * 高级代码清理工具
 * 支持更多选项和自定义配置
 */

const fs = require('fs')
const path = require('path')

/**
 * 清理选项配置
 */
const DEFAULT_OPTIONS = {
  removeComments: true, // 删除注释
  removeEmptyLines: true, // 删除空行
  preserveUserScript: true, // 保留UserScript头部
  trimWhitespace: true, // 去除行尾空白
  compactSpacing: false, // 压缩空格（实验性）
  formatArrays: true, // 格式化数组（对象每行一个）
  outputSuffix: '.min', // 输出文件后缀
  verbose: true, // 详细输出
}

/**
 * 高级代码清理函数
 * @param {string} code - 原始代码
 * @param {Object} options - 清理选项
 * @returns {string} - 清理后的代码
 */
function advancedCleanCode(code, options = {}) {
  const opts = { ...DEFAULT_OPTIONS, ...options }
  const lines = code.split('\n')
  const result = []
  let inUserScript = false
  let inMultiLineComment = false
  let userScriptEnd = false

  for (let i = 0; i < lines.length; i++) {
    let line = lines[i]
    const originalLine = line
    const trimmedLine = line.trim()

    // UserScript头部处理
    if (opts.preserveUserScript) {
      if (trimmedLine === '// ==UserScript==') {
        inUserScript = true
        result.push(line)
        continue
      }

      if (trimmedLine === '// ==/UserScript==') {
        inUserScript = false
        userScriptEnd = true
        result.push(line)
        continue
      }

      if (inUserScript) {
        result.push(line)
        continue
      }
    }

    // 开始清理代码（UserScript头部之后或不保留UserScript时）
    if (!opts.preserveUserScript || userScriptEnd) {
      // 处理多行注释
      if (opts.removeComments && inMultiLineComment) {
        const endIndex = line.indexOf('*/')
        if (endIndex !== -1) {
          inMultiLineComment = false
          line = line.substring(endIndex + 2)
          if (line.trim() === '') continue
        } else {
          continue
        }
      }

      // 检查多行注释开始
      if (opts.removeComments) {
        const multiCommentStart = line.indexOf('/*')
        if (multiCommentStart !== -1) {
          const multiCommentEnd = line.indexOf('*/', multiCommentStart)
          if (multiCommentEnd !== -1) {
            line = line.substring(0, multiCommentStart) + line.substring(multiCommentEnd + 2)
          } else {
            inMultiLineComment = true
            line = line.substring(0, multiCommentStart)
          }
        }
      }

      // 处理单行注释
      if (opts.removeComments) {
        const commentIndex = findSingleLineComment(line)
        if (commentIndex !== -1) {
          line = line.substring(0, commentIndex)
        }
      }

      // 去除行尾空白
      if (opts.trimWhitespace) {
        line = line.trimEnd()
      }

      // 压缩空格（实验性功能）
      if (opts.compactSpacing) {
        line = line.replace(/\s+/g, ' ')
      }

      // 跳过空行
      if (opts.removeEmptyLines && line.trim() === '') {
        continue
      }

      result.push(line)
    }
  }

  let finalCode = result.join('\n')

  // 应用数组格式化
  if (opts.formatArrays) {
    finalCode = formatArrays(finalCode)
  }

  return finalCode
}

/**
 * 格式化数组：对象每行一个，字符串保持原有格式
 * @param {string} code - 代码字符串
 * @returns {string} - 格式化后的代码
 */
function formatArrays(code) {
  // 匹配数组定义
  return code.replace(/(\w+\s*=\s*\[)([\s\S]*?)(\])/g, (_, arrayStart, arrayContent, arrayEnd) => {
    const formattedItems = []
    let currentItem = ''
    let braceCount = 0
    let inString = false
    let stringChar = ''
    let escaped = false

    // 逐字符解析数组内容
    for (let i = 0; i < arrayContent.length; i++) {
      const char = arrayContent[i]

      if (escaped) {
        escaped = false
        currentItem += char
        continue
      }

      if (char === '\\') {
        escaped = true
        currentItem += char
        continue
      }

      if (!inString) {
        if (char === '"' || char === "'" || char === '`') {
          inString = true
          stringChar = char
        } else if (char === '{') {
          braceCount++
        } else if (char === '}') {
          braceCount--
        } else if (char === ',' && braceCount === 0) {
          // 找到数组项分隔符
          const trimmedItem = currentItem.trim()
          if (trimmedItem) {
            formattedItems.push(trimmedItem)
          }
          currentItem = ''
          continue
        }
      } else {
        if (char === stringChar) {
          inString = false
          stringChar = ''
        }
      }

      currentItem += char
    }

    // 处理最后一个项目
    const trimmedItem = currentItem.trim()
    if (trimmedItem) {
      formattedItems.push(trimmedItem)
    }

    if (formattedItems.length === 0) {
      return `${arrayStart}${arrayEnd}`
    }

    // 格式化每个项目
    const formatted = formattedItems.map((item, index) => {
      const isLast = index === formattedItems.length - 1
      const needsComma = !isLast && !item.endsWith(',')

      // 检查是否是对象（包含大括号）
      if (item.includes('{') && item.includes('}')) {
        // 对象：压缩为一行，去除内部换行和多余空格
        const compactObject = item
          .replace(/\s*\n\s*/g, ' ') // 将换行替换为空格
          .replace(/\s+/g, ' ') // 压缩多个空格为一个
          .replace(/{\s+/g, '{ ') // 格式化大括号后的空格
          .replace(/\s+}/g, ' }') // 格式化大括号前的空格
          .replace(/,\s+/g, ', ') // 格式化逗号后的空格
          .replace(/:\s+/g, ': ') // 格式化冒号后的空格
          .trim()

        const finalItem = needsComma ? `${compactObject},` : compactObject
        return `    ${finalItem}`
      } else {
        // 字符串：保持原有格式
        const finalItem = needsComma ? `${item},` : item
        return `    ${finalItem}`
      }
    })

    return `${arrayStart}\n${formatted.join('\n')}\n  ${arrayEnd}`
  })
}

/**
 * 查找单行注释位置
 */
function findSingleLineComment(line) {
  let inString = false
  let stringChar = ''
  let escaped = false

  for (let i = 0; i < line.length - 1; i++) {
    const char = line[i]
    const nextChar = line[i + 1]

    if (escaped) {
      escaped = false
      continue
    }

    if (char === '\\') {
      escaped = true
      continue
    }

    if (!inString) {
      if (char === '"' || char === "'" || char === '`') {
        inString = true
        stringChar = char
      } else if (char === '/' && nextChar === '/') {
        return i
      }
    } else {
      if (char === stringChar) {
        inString = false
        stringChar = ''
      }
    }
  }

  return -1
}

/**
 * 解析命令行参数
 */
function parseArgs(args) {
  const options = { ...DEFAULT_OPTIONS }
  const files = []

  for (let i = 0; i < args.length; i++) {
    const arg = args[i]

    switch (arg) {
      case '--no-comments':
        options.removeComments = false
        break
      case '--keep-empty-lines':
        options.removeEmptyLines = false
        break
      case '--no-userscript':
        options.preserveUserScript = false
        break
      case '--compact':
        options.compactSpacing = true
        break
      case '--no-format-arrays':
        options.formatArrays = false
        break
      case '--suffix':
        options.outputSuffix = args[++i] || '.min'
        break
      case '--quiet':
        options.verbose = false
        break
      case '--help':
      case '-h':
        showHelp()
        process.exit(0)
      default:
        if (!arg.startsWith('--')) {
          files.push(arg)
        }
        break
    }
  }

  return { options, files }
}

/**
 * 显示帮助信息
 */
function showHelp() {
  console.log(`
高级代码清理工具

用法:
  node advanced-cleaner.js [选项] <文件...>

选项:
  --no-comments         不删除注释
  --keep-empty-lines    保留空行
  --no-userscript       不特殊处理UserScript头部
  --compact             压缩空格（实验性）
  --no-format-arrays    不格式化数组（默认会格式化）
  --suffix <后缀>       输出文件后缀（默认: .min）
  --quiet               静默模式
  -h, --help            显示帮助信息

示例:
  node advanced-cleaner.js script.js
  node advanced-cleaner.js --suffix .clean script.js
  node advanced-cleaner.js --no-comments --keep-empty-lines script.js
  node advanced-cleaner.js *.js
`)
}

/**
 * 处理单个文件
 */
function processFile(inputFile, options) {
  const ext = path.extname(inputFile)
  const name = path.basename(inputFile, ext)
  const dir = path.dirname(inputFile)
  const outputFile = path.join(dir, `${name}${options.outputSuffix}${ext}`)

  try {
    if (options.verbose) {
      console.log(`处理: ${inputFile}`)
    }

    const code = fs.readFileSync(inputFile, 'utf8')
    const cleanedCode = advancedCleanCode(code, options)
    fs.writeFileSync(outputFile, cleanedCode, 'utf8')

    if (options.verbose) {
      const originalLines = code.split('\n').length
      const cleanedLines = cleanedCode.split('\n').length
      const originalSize = Buffer.byteLength(code, 'utf8')
      const cleanedSize = Buffer.byteLength(cleanedCode, 'utf8')
      const reduction = (((originalSize - cleanedSize) / originalSize) * 100).toFixed(1)

      console.log(`  → ${outputFile}`)
      console.log(`  行数: ${originalLines} → ${cleanedLines}`)
      console.log(`  大小: ${originalSize} → ${cleanedSize} 字节 (${reduction}%)`)
      console.log('')
    }

    return true
  } catch (error) {
    console.error(`错误处理 ${inputFile}: ${error.message}`)
    return false
  }
}

/**
 * 主函数
 */
function main() {
  const args = process.argv.slice(2)

  if (args.length === 0) {
    showHelp()
    return
  }

  const { options, files } = parseArgs(args)

  if (files.length === 0) {
    console.error('错误: 请指定要处理的文件')
    process.exit(1)
  }

  let successCount = 0

  for (const file of files) {
    if (fs.existsSync(file)) {
      if (processFile(file, options)) {
        successCount++
      }
    } else {
      console.error(`文件不存在: ${file}`)
    }
  }

  if (options.verbose) {
    console.log(`处理完成: ${successCount}/${files.length} 个文件`)
  }
}

if (require.main === module) {
  main()
}

module.exports = { advancedCleanCode }
